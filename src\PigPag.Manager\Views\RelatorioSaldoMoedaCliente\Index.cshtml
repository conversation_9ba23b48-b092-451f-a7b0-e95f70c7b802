<script>
    function PesquisarSaldoClientes(pg) {
        $.ajaxSetup({
            cache: false
        });

        $.ajax({
            url: '@Url.Action("ListarSaldoMoedaCliente", "RelatorioSaldoMoedaCliente")' +
                '?razaoSocial=' + $('#razaoSocial').val() +
                '&nomeFantasia=' + $('#nomeFantasia').val() +
                '&idBanco=' + $('#idBanco').val() +
                '&conta=' + $('#conta').val() +
                '&tipoContaEmpresa=' + $('#tipoContaEmpresa').val() +
                '&itensPorPagina=' + $('#itensPorPagina').val(),
            type: 'GET',
            beforeSend: function () {
                $("#dialogProcessando").modal('show');
            },
            complete: function () {
                $("#dialogProcessando").modal('hide');
            },
            success: function (data) {
                // Limpar a tabela e adicionar os novos dados
                var table = $('#listar-saldos-clientes').DataTable();

                // Se não houver dados, mostrar mensagem
                if (data.length === 0) {
                    table.clear().draw();
                    alert('Nenhum registro encontrado para os filtros informados.');
                } else {
                    table.clear().rows.add(data).draw();
                }

                // Aplicar estilo após carregar os dados
                aplicarEstilo();
            },
            error: function (xhr, status, error) {
                console.error('Erro ao carregar dados:', error);
                alert('Ocorreu um erro ao carregar os dados. Por favor, tente novamente.');
            }
        });
    }

    $(document).ready(function () {
        // Não carrega automaticamente ao entrar na página
    });
</script>

<link href="~/plugins/datatables/PigShop/css/dataTables.bootstrap.min.css" rel="stylesheet" />
<link href="~/plugins/datatables/PigShop/buttons.bootstrap.min.css" rel="stylesheet" />
<script src="~/plugins/datatables/PigShop/js/jquery.dataTables.min.js"></script>
<script src="~/plugins/datatables/PigShop/js/dataTables.bootstrap4.min.js"></script>
<script src="~/plugins/datatables/PigShop/dataTables.buttons.min.js"></script>
<script src="~/plugins/datatables/PigShop/buttons.bootstrap.min.js"></script>
<script src="~/plugins/datatables/buttons.colVis.min.js"></script>
<script src="~/plugins/datatables/buttons.html5.min.js"></script>
<script src="~/plugins/datatables/buttons.print.min.js"></script>
<script src="~/plugins/datatables/jszip.min.js"></script>
<script src="~/plugins/datatables/pdfmake.min.js"></script>
<script src="~/plugins/datatables/vfs_fonts.js"></script>
<script src="~/plugins/datatables/PigShop/moment-with-locales-2.22.2.min.js"></script>

<style>
    .dt-buttons {
        float: right;
    }

    .text_filtro {
        width: 100%;
        padding: 6px 12px;
        border: 1px solid #d2d6de;
        border-radius: 4px;
    }

    .form-group {
        margin-bottom: 15px;
    }
</style>

<section class="content-header">
    <h1>
        Saldos dos Clientes
    </h1>
</section>


<section class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="box box-success">
                <div class="box-header with-border">
                    <h3 class="box-title">Filtros</h3>
                    <div class="box-tools pull-right">
                        <button type="button" class="btn btn-box-tool only-read" data-widget="collapse">
                            <i class="fa fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="box-body" style="display: block;">
                    <div class="row">
                        <div class="col-lg-3">
                            <div class="form-group">
                                Razão Social
                                <input type="text" id="razaoSocial" class="text_filtro only-read" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Nome Fantasia
                                <input type="text" id="nomeFantasia" class="text_filtro only-read" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Banco
                                @Html.DropDownList("idBanco", new PigPag.Common.DropDown.DropDownList().GetDropDownListBancos(), new { @class = "form-control only-read" })
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Conta
                                <input type="text" id="conta" class="text_filtro only-read" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Tipo
                                <select id="tipoContaEmpresa" class="form-control only-read">
                                    <option value="">Todos</option>
                                    <option value="Proprietária">Proprietária</option>
                                    <option value="Transacional">Transacional</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Itens por Pagina
                                <select id="itensPorPagina" name="itensPorPagina" class="form-control only-read">
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                    <option value="500">500</option>
                                    <option value="1000">1.000</option>
                                    <option value="5000">5.000</option>
                                    <option value="99999999">Total</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-4">
                            <button id="btnFiltrar" name="btnFiltrar" type="button" class="btn btn-primary only-read" onclick="PesquisarSaldoClientes(1);"><i class="fa fa-search"></i> Filtrar</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="box box-info">
                <div class="box-body padding">
                    <div class="row" id="conteudo">
                        <div class="col-lg-12">
                            @Html.Partial("Table", new DataTablesModel
                       {
                           Info = true,
                           LengthMenu = "[10, 25, 50, -1], [10, 25, 50, 'Todos']",
                           Name = "listar-saldos-clientes",
                           // Inicializar com array vazio em vez de fazer chamada AJAX automática
                           Data = "[]",
                           Paging = true,
                           Ordering = true,
                           Processing = true,
                           ServerSide = false,
                           Search = false,
                           PrimaryKeyColumn = "IdCliente",
                           OrderColumn = "[1, 'asc'], [2, 'desc'], [4, 'desc']",
                           Length = 50,
                           Dom = "<'row'<'col-md-12't>>" +
                            "<'row margin-t-5'" +
                            "<'col-lg-10 col-xs-12'<'float-lg-left'p>>" +
                            "<'col-lg-2 col-xs-12'<'float-lg-right text-center'i>>" +
                            ">",
                           ColumnCollection = new List<ColumnProperty>
                            {
                               //new ColumnProperty("NomeOperador")
                               // {
                               // Title = "Operador",
                               // Width = "40",
                               //  Searchable = true,
                               //   AutoWidth = true,
                               // },
                               //new ColumnProperty("CodigoCliente")
                               // {
                               // Title = "Código",
                               // Width = "40",
                               //  Searchable = true,
                               //   AutoWidth = true,
                               // },
                                new ColumnProperty("RazaoSocial")
                                {
                                Title = "Razão Social",
                                Width = "250",
                                 Searchable = true,
                                  AutoWidth = true,
                                },
                                new ColumnProperty("NomeFantasia")
                                {
                                Title = "Nome Fantasia",
                                Width = "100",
                                 Searchable = true,
                                  AutoWidth = true
                                },
                                new ColumnProperty("Banco")
                                {
                                Title = "Banco",
                                Width = "50",
                                 Searchable = true,
                                  AutoWidth = true,
                                },
                                new ColumnProperty("Conta")
                                {
                                Title = "Conta",
                                Width = "50",
                                 Searchable = true,
                                  AutoWidth = true,
                                },
                                new ColumnProperty("TipoContaEmpresa")
                                {
                                Title = "Tipo",
                                Width = "60",
                                    Searchable = true,
                                    AutoWidth = true,
                                },
                                new ColumnProperty("Saldo")
                                {
                                    Title = "Saldo na Connect",
                                    Width = "60",
                                    Searchable = true,
                                    AutoWidth = true,
                                    Render = new RenderCurrency("R$"),
                                },
                                new ColumnProperty("SaldoBanco")
                                {
                                    Title = "Saldo no Banco",
                                    Width = "60",
                                    Searchable = true,
                                    AutoWidth = true,
                                    Render = new RenderCurrency("R$"),
                                },
                                new ColumnProperty("TarifasPendentes")
                                {
                                    Title = "Tarifas Pendentes",
                                    Width = "60",
                                    Searchable = true,
                                    AutoWidth = true,
                                    Render = new RenderCurrency("R$"),
                                }
                                }
                       })
                            <script>
                                function aplicarEstilo() {
                                    $('td').filter(function () {
                                        return $(this).text().includes('R$');
                                    }).css('text-align', 'right');
                                    $('th').filter(function () {
                                        return $(this).text().includes('Saldo');
                                    }).css('text-align', 'right');
                                }

                                // Configura o MutationObserver para observar alterações no DOM
                                const observer = new MutationObserver(function (mutationsList) {
                                    aplicarEstilo();
                                });

                                // Observa mudanças no corpo da tabela
                                observer.observe(document.body, { childList: true, subtree: true });
                            </script>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modal de processamento -->
<div class="modal fade" id="dialogProcessando" tabindex="-1" role="dialog" aria-labelledby="dialogProcessandoLabel" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="dialogProcessandoLabel">Processando</h4>
            </div>
            <div class="modal-body">
                <div class="progress">
                    <div class="progress-bar progress-bar-striped active" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%">
                        <span class="sr-only">Processando</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>