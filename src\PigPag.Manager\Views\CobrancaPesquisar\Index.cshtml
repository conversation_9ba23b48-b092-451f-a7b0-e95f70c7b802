<script>

    function PesquisarCobranca(pg) {

        $.ajaxSetup({
            cache: false
        });

        $.ajax({
            url: '/CobrancaPesquisar/_PartialPesquisar/?pg=' + pg +
                '&codigoCobranca=' + $('#codigoCobranca').val() +
                '&numeroFatura=' + $('#numeroFatura').val() +
                '&dataCriacaoInicial=' + $('#dataCriacaoInicial').val() +
                '&dataCriacaoFinal=' + $('#dataCriacaoFinal').val() +
                '&nomePagador=' + $('#nomePagador').val() +
                '&cpfcnpjPagador=' + $('#cpfcnpjPagador').val() +
                '&endtoend=' + $('#endtoend').val() +
                '&nomeCliente=' + $('#nomeCliente').val() +
                '&txidPIX=' + $('#txidPIX').val() + 
                '&itensPorPagina=' + $('#itensPorPagina').val() + 
                '&statusCobranca=' + $('#idStatus').val() +
                '&idBanco=' + $('#idBanco').val(),
            type: 'GET',
            beforeSend: function ()
            {
                $("#dialogProcessando").modal('show');
            },
            complete: function ()
            {
                $("#dialogProcessando").modal('hide');
            },
            success: function (response)
            {
                $('#conteudo-cobranca').html(response);
            }
        });
    }

    $(document).ready(function () {
        PesquisarCobranca(1);
    });

</script>

<section class="content-header">
    <h1>
        Pesquisar Cobranças
    </h1>
</section>

<section class="content">
    <div class="row">
        <div class="col-lg-12">
            <div class="box box-success">
                <div class="box-header with-border">
                    <h3 class="box-title">@Resource.Filtros</h3>
                    <div class="box-tools pull-right">
                        <button type="button" class="btn btn-box-tool only-read" data-widget="collapse">
                            <i class="fa fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="box-body" style="display: block;">
                    <div class="row">
                        <div class="col-lg-3">
                            <div class="form-group">
                                Código Cobrança
                                <input type="text" id="codigoCobranca" class="text_filtro only-read" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Número Fatura (CustomId)
                                <input type="text" id="numeroFatura" class="text_filtro only-read" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Data (Início)
                                <input id="dataCriacaoInicial" name="dataCriacaoInicial" value="@DateTime.Now.ToString("dd/MM/yyyy 00:00")" type="text" class="form-control only-read" data-inputmask="'alias': 'dd/MM/yyyy HH:mm'" data-mask="" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Data (Fim)
                                <input id="dataCriacaoFinal" name="dataCriacaoFinal" value="@DateTime.Now.ToString("dd/MM/yyyy 23:59")" type="text" class="form-control only-read" data-inputmask="'alias': 'dd/MM/yyyy HH:mm'" data-mask="" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Nome do Cliente
                                <input type="text" id="nomeCliente" class="text_filtro only-read" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Nome do Pagador
                                <input type="text" id="nomePagador" class="text_filtro only-read" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                CPF/CNPJ do Pagador
                                <input type="text" id="cpfcnpjPagador" class="text_filtro only-read" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                TxId PIX
                                <input type="text" id="txidPIX" class="text_filtro only-read" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                EndToEnd
                                <input id="endtoend" name="endtoend" value="" type="text" class="form-control only-read" />
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Banco
                                @Html.DropDownList("idBanco", new PigPag.Common.DropDown.DropDownList().GetDropDownListBancos(), new { @class = "form-control only-read" })
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Status
                                @Html.DropDownList("idStatus", new PigPag.Common.DropDown.DropDownList().GetDropDownListStatus(), new { @class = "form-control only-read" })
                            </div>
                        </div>
                        <div class="col-lg-3">
                            <div class="form-group">
                                Itens por Pagina
                                <select id="itensPorPagina" name="itensPorPagina" class="form-control only-read">
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                    <option value="500">500</option>
                                    <option value="1000">1.000</option>
                                    <option value="5000">5.000</option>
                                    <option value="99999999">Total</option>
                                </select>

                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-4">
                            <button id="btnFiltrar" name="btnFiltrar" type="button" class="btn btn-primary only-read" onclick="PesquisarCobranca(1);"><i class="fa fa-search"></i> Filtrar</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="box box-info">
                <div class="box-body">
                    @if (ViewBag.Mensagem != null)
                    {
                        <script>
                            $(function () {
                                MensagemOK('@Html.Raw(ViewBag.Mensagem)');
                            });
                        </script>
                    }
                    <div class="row">
                        <div class="col-lg-12 table-responsive" id="conteudo-cobranca">
                            <table class="table table-hover table-striped">
                                <thead>
                                    <tr>
                                        <th class="text-center">Consultar <br /><input type="checkbox" id="reprocessarTudo" /></th>
                                        <th class="text-center">Reenviar <br /><input type="checkbox" id="reenviarTudo" /></th>
                                        <th class="text-left">Cliente</th>
                                        <th class="text-left">Código</th>
                                        <th class="text-left">Fatura</th>
                                        <th class="text-center">Comprovante</th>
                                        <th>Nome</th>
                                        <th class="text-center">Valor Líquido</th>
                                        <th class="text-center">Valor Pago</th>
                                        <th class="text-center">Cadastro</th>
                                        <th class="text-center">Vencimento</th>
                                        <th class="text-center">Pagamento</th>
                                        <th class="text-center">Cancelamento</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="14" style="text-align:center">Carregando...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

