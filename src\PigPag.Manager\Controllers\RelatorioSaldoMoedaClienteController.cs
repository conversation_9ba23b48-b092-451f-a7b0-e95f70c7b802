using PigPag.Domain.Manager;
using System;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace PigPag.Manager.Controllers
{
    public class RelatorioSaldoMoedaClienteController : BaseAutorizacaoController
    {
        public virtual ActionResult Index()
        {
            try
            {
                return View();
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public virtual async Task<ActionResult> ListarSaldoMoedaCliente(string razaoSocial = null, string nomeFantasia = null, int? idBanco = null, string conta = null, string tipoContaEmpresa = null, int itensPorPagina = 25)
        {
            try
            {
                var usuarioLogado = new UsuarioAdministrativo().SelectUsuarioAdministrativoLogado();
                var data = await new SaldoMoedaCliente().SelectSaldoMoedaCliente(SaldoMoedaCliente.Moeda.Real, usuarioLogado.IdOperador, razaoSocial, nomeFantasia, idBanco, conta, tipoContaEmpresa);

                foreach (var record in data)
                    record.SaldoBanco = (decimal)(await sharedService.GetBalanceAsync(record.IdContaBancariaEmpresa)).Balance;

                var json = Json(data, JsonRequestBehavior.AllowGet);
                json.MaxJsonLength = Int32.MaxValue;

                return json;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}